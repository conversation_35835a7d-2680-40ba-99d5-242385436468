"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/layout";
import {
  Trophy,
  TrendingUp,
  BarChart3,
  Target,
  Clock,
  Award,
  Thermometer,
  Activity,
  Zap,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@/components/ui/navigation";
import UserLevelBadge from "@/components/features/gamification/user-level-badge";
import { useGamification } from "@/lib/hooks/use-gamification";
import {
  StudentOnly,
  TeacherOnly,
  AdminOnly,
} from "@/components/features/auth/role-guard";
import { gamificationService } from "@/lib/services";
import {
  TimeSeriesChart,
  AdvancedScoreDistributionChart,
  CompletionFunnelChart,
  DifficultyHeatmapChart,
  TimeScoreCorrelationChart,
  ActivityTimelineChart,
  AnalyticsSummaryCard,
} from "@/components/features/charts";

export default function DashboardPage() {
  const { userGamification, isLoading } = useGamification();

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Chào mừng bạn quay trở lại!</p>
        </div>
      </div>

      {/* Student Gamification Section */}
      <StudentOnly>
        {isLoading ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="space-y-0 pb-2">
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : userGamification ? (
          <div className="space-y-6">
            {/* Level Badge */}
            <UserLevelBadge variant="detailed" />

            {/* Stats Grid */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <Card className="border-2 hover:border-primary transition-all">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Tổng Điểm
                  </CardTitle>
                  <Trophy className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {gamificationService.formatPoints(
                      userGamification.total_points
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Cấp độ {userGamification.current_level}
                  </p>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary transition-all">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Độ Chính Xác
                  </CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {gamificationService.calculateAccuracyRate(
                      userGamification.stats.total_correct_answers,
                      userGamification.stats.total_questions_answered
                    )}
                    %
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {userGamification.stats.total_correct_answers}/
                    {userGamification.stats.total_questions_answered} câu đúng
                  </p>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary transition-all">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Streak Tốt Nhất
                  </CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {userGamification.stats.best_streak}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Hiện tại: {userGamification.stats.current_streak}
                  </p>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary transition-all">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Quiz Hoàn Thành
                  </CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {userGamification.stats.total_quizzes_completed}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {userGamification.stats.perfect_scores} điểm tuyệt đối
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Achievement Cards */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="border-2 hover:border-primary transition-all">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="w-5 h-5 text-yellow-500" />
                    Thành Tích Nổi Bật
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center p-3 rounded-lg bg-green-50 dark:bg-green-950/20">
                    <div>
                      <p className="font-medium text-green-700 dark:text-green-300">
                        Điểm Tuyệt Đối
                      </p>
                      <p className="text-sm text-green-600 dark:text-green-400">
                        Hoàn thành quiz với 100% độ chính xác
                      </p>
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      {userGamification.stats.perfect_scores}
                    </div>
                  </div>

                  <div className="flex justify-between items-center p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20">
                    <div>
                      <p className="font-medium text-blue-700 dark:text-blue-300">
                        Bonus Tốc Độ
                      </p>
                      <p className="text-sm text-blue-600 dark:text-blue-400">
                        Trả lời nhanh dưới 5 giây
                      </p>
                    </div>
                    <div className="text-2xl font-bold text-blue-600">
                      {userGamification.stats.speed_bonus_earned}
                    </div>
                  </div>

                  <div className="flex justify-between items-center p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20">
                    <div>
                      <p className="font-medium text-purple-700 dark:text-purple-300">
                        Thời Gian Trung Bình
                      </p>
                      <p className="text-sm text-purple-600 dark:text-purple-400">
                        Thời gian trả lời mỗi câu hỏi
                      </p>
                    </div>
                    <div className="text-2xl font-bold text-purple-600">
                      {userGamification.stats.average_response_time
                        ? gamificationService.formatResponseTime(
                            userGamification.stats.average_response_time
                          )
                        : "N/A"}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary transition-all">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5 text-blue-500" />
                    Tiến Độ Học Tập
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Tiến độ lên cấp</span>
                      <span>{userGamification.experience_points}/100 XP</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${
                            (userGamification.experience_points / 100) * 100
                          }%`,
                        }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Còn {userGamification.experience_to_next_level} XP để lên
                      cấp {userGamification.current_level + 1}
                    </p>
                  </div>

                  <div className="pt-4 border-t space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Tổng câu hỏi</span>
                      <span className="font-semibold">
                        {userGamification.stats.total_questions_answered}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Câu trả lời đúng</span>
                      <span className="font-semibold text-green-600">
                        {userGamification.stats.total_correct_answers}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Streak hiện tại</span>
                      <span className="font-semibold text-orange-600">
                        {userGamification.stats.current_streak}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Trophy className="w-16 h-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Chưa có dữ liệu</h3>
              <p className="text-muted-foreground text-center">
                Hãy tham gia quiz để bắt đầu hành trình học tập của bạn!
              </p>
            </CardContent>
          </Card>
        )}
      </StudentOnly>

      {/* Teacher Analytics Dashboard */}
      <TeacherOnly>
        <div className="space-y-6">
          {/* Analytics Summary */}
          <AnalyticsSummaryCard className="w-full" />

          {/* Analytics Tabs */}
          <Tabs defaultValue="performance" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger
                value="performance"
                className="flex items-center gap-1 text-xs lg:text-sm"
              >
                <TrendingUp className="h-3 w-3 lg:h-4 lg:w-4" />
                <span className="hidden sm:inline">Hiệu suất</span>
                <span className="sm:hidden">HS</span>
              </TabsTrigger>
              <TabsTrigger
                value="difficulty"
                className="flex items-center gap-1 text-xs lg:text-sm"
              >
                <Thermometer className="h-3 w-3 lg:h-4 lg:w-4" />
                <span className="hidden sm:inline">Độ khó</span>
                <span className="sm:hidden">ĐK</span>
              </TabsTrigger>
              <TabsTrigger
                value="behavior"
                className="flex items-center gap-1 text-xs lg:text-sm"
              >
                <Activity className="h-3 w-3 lg:h-4 lg:w-4" />
                <span className="hidden sm:inline">Hành vi</span>
                <span className="sm:hidden">HV</span>
              </TabsTrigger>
              <TabsTrigger
                value="correlation"
                className="flex items-center gap-1 text-xs lg:text-sm"
              >
                <Zap className="h-3 w-3 lg:h-4 lg:w-4" />
                <span className="hidden sm:inline">Tương quan</span>
                <span className="sm:hidden">TQ</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="performance" className="space-y-6 mt-6">
              <div className="mb-4 p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                <h3 className="font-medium text-green-900 mb-1">
                  Dashboard Analytics & Hiệu suất
                </h3>
                <p className="text-sm text-green-700">
                  Tổng quan về hiệu suất học tập, xu hướng điểm số và phân tích
                  chi tiết các chỉ số quan trọng.
                </p>
              </div>
              {/* Kết hợp cả tổng quan và hiệu suất chi tiết */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <TimeSeriesChart className="w-full" />
                <CompletionFunnelChart className="w-full" />
              </div>
              <AdvancedScoreDistributionChart className="w-full" />
            </TabsContent>

            <TabsContent value="difficulty" className="space-y-6 mt-6">
              <div className="mb-4 p-4 bg-orange-50 rounded-lg border-l-4 border-orange-500">
                <h3 className="font-medium text-orange-900 mb-1">
                  Phân tích Độ khó
                </h3>
                <p className="text-sm text-orange-700">
                  Bản đồ nhiệt độ khó của các câu hỏi theo chương và mức độ,
                  giúp xác định nội dung cần cải thiện.
                </p>
              </div>
              <DifficultyHeatmapChart className="w-full" />
            </TabsContent>

            <TabsContent value="behavior" className="space-y-6 mt-6">
              <div className="mb-4 p-4 bg-purple-50 rounded-lg border-l-4 border-purple-500">
                <h3 className="font-medium text-purple-900 mb-1">
                  Phân tích Hành vi
                </h3>
                <p className="text-sm text-purple-700">
                  Dòng thời gian hoạt động của học viên, mẫu hình học tập và xu
                  hướng tham gia.
                </p>
              </div>
              <ActivityTimelineChart className="w-full" />
            </TabsContent>

            <TabsContent value="correlation" className="space-y-6 mt-6">
              <div className="mb-4 p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                <h3 className="font-medium text-yellow-900 mb-1">
                  Phân tích Tương quan
                </h3>
                <p className="text-sm text-yellow-700">
                  Mối quan hệ giữa thời gian làm bài và điểm số, phát hiện các
                  mẫu hình bất thường.
                </p>
              </div>
              <TimeScoreCorrelationChart className="w-full" />
            </TabsContent>
          </Tabs>
        </div>
      </TeacherOnly>

      <AdminOnly>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card className="border-2 hover:border-primary transition-all">
            <CardHeader>
              <CardTitle>Quản lý Người dùng</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Quản lý tài khoản và phân quyền
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 hover:border-primary transition-all">
            <CardHeader>
              <CardTitle>Hệ thống</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Cấu hình và giám sát hệ thống
              </p>
            </CardContent>
          </Card>

          <Card className="border-2 hover:border-primary transition-all">
            <CardHeader>
              <CardTitle>Báo cáo Tổng quan</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Thống kê toàn hệ thống</p>
            </CardContent>
          </Card>

          <Card className="border-2 hover:border-primary transition-all">
            <CardHeader>
              <CardTitle>Cài đặt</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Cấu hình hệ thống và tính năng
              </p>
            </CardContent>
          </Card>
        </div>
      </AdminOnly>
    </div>
  );
}
