# Task 2.3: <PERSON><PERSON> thống Trứng thưởng & <PERSON><PERSON><PERSON> tầm - Implementation Guide

## Tổng quan
Task 2.3 triển khai hệ thống trứng thưởng hoàn chỉnh với 24 loại trứng kh<PERSON>c nhau, hệ thống phần thưởng dựa trên x<PERSON><PERSON>, và tích hợp với tất cả các hệ thống gamification hiện có.

## Kiến trúc hệ thống

### 1. Database Schema
- **EggTypes**: 24 loại trứng với độ hiếm khác nhau
- **EggRewards**: Phần thưởng có thể có trong từng loại trứng
- **UserEggs**: Trứng người dùng sở hữu
- **EggDropRules**: Quy tắc rơi trứng dựa trên trigger
- **EggOpeningHistory**: Lịch sử mở trứng và phần thưởng

### 2. Models
- `EggType`: <PERSON><PERSON><PERSON><PERSON> lý các loại trứng
- `EggReward`: <PERSON><PERSON><PERSON><PERSON> lý phần thưởng trong trứng
- `UserEgg`: <PERSON><PERSON><PERSON><PERSON> lý trứng của người dùng
- `EggDropRule`: Quản lý quy tắc rơi trứng
- `EggOpeningHistory`: Lịch sử mở trứng

### 3. Service Layer
- `EggRewardService`: Logic nghiệp vụ chính cho hệ thống trứng

### 4. API Endpoints
- `/api/eggs/*`: Tất cả endpoints liên quan đến trứng

## Tính năng chính

### 1. Hệ thống 24 loại trứng
```
COMMON (3 loại):
- Basic Egg, Cracked Egg, Spotted Egg

UNCOMMON (4 loại):
- Coconut Egg, Cat Egg, Dog Egg, Party Egg

RARE (5 loại):
- Ice Egg, Royal Egg, Sandcastle Egg, Mine Egg, Yeti Egg

EPIC (5 loại):
- Dragon Egg, Rainbow Egg, Angel Egg, Demon Egg, Kraken Egg

LEGENDARY (4 loại):
- Legendary Egg, Angel Demon Egg, Dominus Egg, Black Hole Egg

MYTHICAL (3 loại):
- Mythical Egg, Secret Egg, Glitched Egg
```

### 2. Hệ thống phần thưởng
- **Currency**: SynCoin, Kristal, XP
- **Items**: Avatar, Frame, Emoji, Name Effect
- **Duplicate Conversion**: Tự động chuyển đổi item trùng thành Kristal

### 3. Cơ chế rơi trứng
- **Quiz Completion**: Dựa trên điểm số và độ chính xác
- **Perfect Score**: Đảm bảo nhận trứng khi đạt điểm tuyệt đối
- **Streak Achievement**: Thưởng cho chuỗi thắng
- **Level Up**: Trứng thưởng khi lên cấp
- **Daily Login**: Trứng đăng nhập hàng ngày

### 4. Cửa hàng trứng
- Mua trứng bằng SynCoin hoặc Kristal
- Giá cả dựa trên độ hiếm
- Giới hạn số lượng mua

## API Documentation

### Inventory Management
```javascript
// Get user egg inventory
GET /api/eggs/inventory

// Response
{
  "success": true,
  "data": {
    "inventory": [
      {
        "egg_type": { /* EggType info */ },
        "quantity": 3,
        "eggs": [ /* UserEgg objects */ ],
        "latest_obtained": "2024-01-15T10:30:00Z"
      }
    ],
    "total_egg_types": 5,
    "total_eggs": 12
  }
}
```

### Egg Opening
```javascript
// Open an egg
POST /api/eggs/open
{
  "user_egg_id": 123
}

// Response
{
  "success": true,
  "data": {
    "egg_type": { /* EggType info */ },
    "rewards": [
      {
        "reward_type": "SYNCOIN",
        "reward_amount": 100,
        "was_duplicate": false,
        "kristal_compensation": 0
      }
    ],
    "summary": {
      "total_rewards": 2,
      "syncoin_earned": 100,
      "kristal_earned": 15,
      "items_received": 1,
      "duplicates_converted": 5
    }
  }
}
```

### Shop System
```javascript
// Get egg shop
GET /api/eggs/shop

// Purchase egg
POST /api/eggs/purchase
{
  "egg_type_id": 1,
  "quantity": 2
}
```

### Egg Awarding (Internal)
```javascript
// Award eggs based on triggers
POST /api/eggs/award
{
  "trigger_type": "QUIZ_COMPLETION",
  "trigger_data": {
    "score": 85,
    "min_correct": 8,
    "total_questions": 10
  }
}
```

## Integration với các hệ thống khác

### 1. Quiz System
```javascript
// Trong QuizResultController, sau khi lưu kết quả:
const eggResult = await EggRewardService.awardEggByTrigger(
  userId, 
  'QUIZ_COMPLETION', 
  {
    score: finalScore,
    min_correct: correctAnswers,
    total_questions: totalQuestions
  }
);
```

### 2. Level System
```javascript
// Trong User model, khi lên cấp:
if (leveledUp) {
  await EggRewardService.awardEggByTrigger(
    this.user_id,
    'LEVEL_UP',
    { level_milestone: newLevel }
  );
}
```

### 3. Currency System
- Tự động tích hợp với CurrencyService
- Duplicate items tự động chuyển thành Kristal
- Mua trứng tự động trừ tiền

### 4. Avatar System
- Items từ trứng tự động thêm vào UserInventory
- Tích hợp với AvatarCustomizationService

## Cài đặt và triển khai

### 1. Chạy SQL Schema
```sql
-- Chạy file SQL để tạo tables và data
\i doc_for_devgame/task2/gamification_egg_system.sql
```

### 2. Restart Server
```bash
# Restart Node.js server để load models mới
npm restart
```

### 3. Test API
```bash
# Test với Postman hoặc curl
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X GET http://localhost:3000/api/eggs/inventory
```

## Tính năng nâng cao

### 1. Probability System
- Drop rates được tính toán chính xác
- Weighted random selection
- Guaranteed rewards cho một số trường hợp

### 2. Daily Limits
- Giới hạn số trứng nhận được mỗi ngày
- Reset tự động vào 00:00

### 3. Analytics
- Tracking lịch sử mở trứng
- Thống kê phần thưởng
- Views cho báo cáo

### 4. Anti-Duplicate System
- Tự động detect items trùng
- Chuyển đổi thành Kristal với tỷ lệ hợp lý
- Thông báo cho user về conversion

## Testing Scenarios

### 1. Basic Flow
1. User hoàn thành quiz → Nhận trứng
2. Mở trứng → Nhận rewards
3. Duplicate item → Chuyển thành Kristal

### 2. Shop Flow
1. Check balance → Mua trứng
2. Trừ tiền → Thêm trứng vào inventory
3. Mở trứng → Nhận rewards

### 3. Integration Flow
1. Level up → Trigger egg drop
2. Perfect score → Guaranteed egg
3. Daily login → Daily egg

## Lưu ý quan trọng

1. **Performance**: Sử dụng indexes cho queries thường xuyên
2. **Security**: Validate tất cả inputs
3. **Consistency**: Transaction cho operations quan trọng
4. **Scalability**: Pagination cho large datasets
5. **Monitoring**: Log tất cả egg operations

## Next Steps
- Tích hợp với frontend UI
- Thêm animations cho egg opening
- Implement push notifications
- Add more egg types theo feedback

---
**Status**: ✅ Completed - Ready for testing
**Dependencies**: Currency System, Avatar System, User System
**API Docs**: Available at `/api/eggs/docs`
