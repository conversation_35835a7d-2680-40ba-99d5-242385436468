// Types cho Chapter Analytics API responses - Updated to match actual API structure

// Content type definition
export type ContentType = "text" | "video" | "exercise";

// Performance level definition
export type PerformanceLevel = "excellent" | "good" | "average" | "weak";

// Question interface for analytics
export interface AnalyticsQuestion {
  question_id: number;
  is_correct: boolean;
  time_spent: number;
  lo_name?: string;
}

// Section interface
export interface Section {
  section_id: number;
  title: string;
  content?: string;
  order?: number | null;
  content_type?: ContentType;
  has_content?: boolean;
}

// Chapter analysis item interface
export interface ChapterAnalysisItem {
  chapter_id: number;
  chapter_name: string;
  chapter_description?: string;
  sections?: Section[];
  total_questions: number;
  correct_answers: number;
  total_time_spent: number;
  related_los: string[];
  questions: AnalyticsQuestion[];
  accuracy_percentage: number;
  average_time_per_question: number;
  performance_level: PerformanceLevel;
  los_covered?: number;
}

// Learning Outcome analysis item interface
export interface LearningOutcomeAnalysisItem {
  lo_id: number;
  lo_name: string;
  lo_description: string;
  total_questions: number;
  correct_answers: number;
  total_time_spent: number;
  questions: AnalyticsQuestion[];
  accuracy_percentage: number;
  average_time_per_question: number;
  performance_level: PerformanceLevel;
}

// Difficulty analysis item interface
export interface DifficultyAnalysisItem {
  level_id: number;
  level_name: string;
  total_questions: number;
  correct_answers: number;
  total_time_spent: number;
  questions: AnalyticsQuestion[];
  accuracy_percentage: number;
  average_time_per_question: number;
  performance_level: PerformanceLevel;
}

// Legacy interfaces (keeping for backward compatibility)
export interface SectionRecommendation {
  section_id: number;
  section_name: string;
  content_type: ContentType;
  mastery_level: number;
  recommendation: string;
  priority: "high" | "medium" | "low";
  estimated_study_time: number; // in minutes
}

// Main interfaces cho API responses

// Interface cho /quiz-results/detailed-analysis/:quiz_id/:user_id
export interface ChapterAnalysisData {
  quiz_info: {
    quiz_id: number;
    quiz_name: string;
    subject: {
      subject_id: number;
      name: string;
      description?: string;
    };
    total_questions: number;
    completion_date: string;
  };
  student_info: {
    user_id: number;
    name: string;
    email: string;
  };
  overall_performance: {
    final_score: number;
    total_questions_answered: number;
    correct_answers: number;
    accuracy_percentage: number;
    total_time_spent_seconds: number;
    average_time_per_question_seconds: number;
    performance_level: PerformanceLevel;
  };
  question_distribution: {
    by_chapter: Array<{
      chapter_id: number;
      chapter_name: string;
      question_count: number;
      percentage: number;
      related_los: string[];
    }>;
    by_difficulty: any[];
    total_questions: number;
  };
  chapter_analysis: {
    strengths: ChapterAnalysisItem[];
    weaknesses: ChapterAnalysisItem[];
    neutral: ChapterAnalysisItem[];
    overall_stats: {
      total_chapters_tested: number;
      strong_chapters: number;
      weak_chapters: number;
      neutral_chapters: number;
    };
    summary: {
      total_chapters_covered: number;
      strong_chapters_count: number;
      weak_chapters_count: number;
      chapters_needing_attention: Array<{
        chapter_id: number;
        chapter_name: string;
        accuracy: number;
        gap_to_target: number;
        related_los: string[];
        sections: Section[];
      }>;
    };
  };
  learning_outcome_analysis: {
    strengths: LearningOutcomeAnalysisItem[];
    weaknesses: LearningOutcomeAnalysisItem[];
    neutral: LearningOutcomeAnalysisItem[];
    overall_stats: {
      total_los_tested: number;
      strong_los: number;
      weak_los: number;
      neutral_los: number;
    };
    summary: {
      total_los_covered: number;
      strong_areas_count: number;
      weak_areas_count: number;
      areas_needing_attention: any[];
    };
  };
  difficulty_analysis: {
    strengths: DifficultyAnalysisItem[];
    weaknesses: DifficultyAnalysisItem[];
    neutral: DifficultyAnalysisItem[];
    overall_stats: {
      total_levels_tested: number;
      strong_levels: number;
      weak_levels: number;
      neutral_levels: number;
    };
    summary: {
      total_levels_tested: number;
      strong_levels_count: number;
      weak_levels_count: number;
      challenging_areas: Array<{
        level_name: string;
        accuracy: number;
        improvement_needed: number;
        note: string;
      }>;
    };
  };
  improvement_suggestions: {
    priority_areas: string[];
    study_plan: Array<{
      phase: string;
      focus: string;
      activities: string[];
    }>;
    recommended_chapters: any[];
    learning_strategies: string[];
  };
  learning_insights: {
    what_you_did_well: string;
    areas_for_improvement: string;
    next_steps: string;
    study_chapters: Array<{
      chapter_name: string;
      accuracy: number;
      sections_to_review: string[];
      related_concepts: string[];
      note: string;
    }>;
  };
  generated_at: string;
}

// Interface cho /reports/subject/:subject_id/comprehensive-analysis/:user_id - ACTUAL API STRUCTURE
export interface ComprehensiveAnalysisData {
  subject_info: {
    subject_id: number;
    subject_name: string;
    description: string;
    total_quizzes: number;
    completed_quizzes: number;
  };
  student_info: {
    user_id: number;
    name: string;
    email: string;
  };
  overall_performance: {
    total_questions_answered: number;
    correct_answers: number;
    overall_accuracy_percentage: number;
    average_quiz_score: number;
    total_time_spent_seconds: number;
    performance_level: "excellent" | "good" | "average" | "weak";
  };
  chapter_completion_chart: {
    labels: string[];
    completion_percentages: number[];
    target_line: number;
    chart_data: Array<{
      chapter_id: number;
      chapter_name: string;
      completion_percentage: number;
      status: "achieved" | "in_progress" | "needs_attention";
      gap_to_target: number;
      related_los: string[];
      sections: Array<{
        section_id: number;
        title: string;
        content_type: "text" | "video" | "exercise";
        has_content: boolean;
        order: number | null;
      }>;
    }>;
  };
  chapter_analysis: {
    strengths: Array<ChapterAnalysisItem>;
    weaknesses: Array<ChapterAnalysisItem>;
    neutral: Array<ChapterAnalysisItem>;
    overall_stats: {
      total_chapters_tested: number;
      strong_chapters: number;
      weak_chapters: number;
      neutral_chapters: number;
    };
    achievement_summary: {
      total_chapters: number;
      achieved_chapters: number;
      in_progress_chapters: number;
      needs_attention_chapters: number;
    };
  };
  learning_outcome_analysis: {
    strengths: Array<LearningOutcomeAnalysisItem>;
    weaknesses: Array<LearningOutcomeAnalysisItem>;
    neutral: Array<LearningOutcomeAnalysisItem>;
    overall_stats: {
      total_los_tested: number;
      strong_los: number;
      weak_los: number;
      neutral_los: number;
    };
    achievement_summary: {
      total_los: number;
      achieved_los: number;
      weak_los: number;
      neutral_los: number;
    };
  };
  improvement_suggestions: {
    priority_areas: string[];
    study_plan: Array<{
      phase: string;
      focus: string;
      activities: string[];
    }>;
    recommended_chapters: string[];
    learning_strategies: string[];
  };
  quiz_breakdown: Array<{
    quiz_id: number;
    quiz_name: string;
    score: number;
    completion_date: string;
    status: "completed" | "in_progress" | "not_started";
  }>;
  learning_insights: {
    subject_mastery_level: string;
    strongest_chapters: string[];
    chapters_needing_improvement: string[];
    recommended_focus: string;
    next_learning_phase: string;
    study_recommendations: Array<{
      chapter_name: string;
      current_accuracy: number;
      sections_to_review: string[];
      related_concepts: string[];
      priority: "high" | "medium" | "low" | "none";
      note: string;
    }>;
  };
  generated_at: string;
}

// Interface cho /teacher-analytics/quiz/:quizId/comprehensive-report
export interface TeacherAnalyticsData {
  quiz_id: number;
  quiz_name: string;
  class_info: {
    class_id: number;
    class_name: string;
    total_students: number;
    completed_students: number;
  };
  quiz_statistics: {
    average_score: number;
    highest_score: number;
    lowest_score: number;
    median_score: number;
    standard_deviation: number;
    completion_rate: number;
    average_time: number; // in seconds
  };
  chapter_performance: Array<{
    chapter_id: number;
    chapter_name: string;
    class_average: number;
    difficulty_rating: "easy" | "medium" | "hard";
    section_breakdown: Array<{
      section_id: number;
      section_name: string;
      content_type: "text" | "video" | "exercise";
      average_score: number;
      students_struggling: number;
      common_mistakes: string[];
    }>;
  }>;
  student_insights: Array<{
    user_id: number;
    student_name: string;
    overall_score: number;
    strengths: string[];
    areas_for_improvement: string[];
    recommended_actions: string[];
    risk_level: "low" | "medium" | "high";
  }>;
  teaching_recommendations: {
    focus_areas: string[];
    suggested_activities: Array<{
      chapter_id: number;
      activity_type: "review" | "practice" | "remediation";
      description: string;
      estimated_time: number; // in minutes
    }>;
    class_interventions: Array<{
      priority: "high" | "medium" | "low";
      intervention: string;
      target_students: number[];
      expected_outcome: string;
    }>;
  };
}

// Common response wrapper interface
export interface ChapterAnalyticsResponse<T> {
  success: boolean;
  data: T;
  message: string;
  timestamp: string;
}

// Error response interface
export interface ChapterAnalyticsError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: string;
  };
  timestamp: string;
}

// API method parameter interfaces
export interface DetailedAnalysisParams {
  quiz_id: number;
  user_id: number;
}

export interface ComprehensiveAnalysisParams {
  subject_id: number;
  user_id: number;
  start_date?: string;
  end_date?: string;
}

export interface TeacherAnalyticsParams {
  quiz_id: number;
  include_student_details?: boolean;
  include_recommendations?: boolean;
}
