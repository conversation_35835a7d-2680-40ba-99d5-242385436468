/**
 * Integration verification script for Chapter Analytics API
 * This script verifies that all new chapter analytics endpoints work correctly
 * and that backward compatibility is maintained.
 */

import { chapterAnalyticsService } from "@/lib/services/api";
import { quizService } from "@/lib/services/api";

// Test data - replace with actual IDs from your system
const TEST_DATA = {
  quiz_id: 1,
  user_id: 1,
  subject_id: 1,
};

interface VerificationResult {
  test: string;
  status: "PASS" | "FAIL" | "SKIP";
  message: string;
  duration?: number;
}

class ChapterAnalyticsVerifier {
  private results: VerificationResult[] = [];

  private addResult(
    test: string,
    status: "PASS" | "FAIL" | "SKIP",
    message: string,
    duration?: number
  ) {
    this.results.push({ test, status, message, duration });
    const emoji = status === "PASS" ? "✅" : status === "FAIL" ? "❌" : "⏭️";
    console.log(
      `${emoji} ${test}: ${message}${duration ? ` (${duration}ms)` : ""}`
    );
  }

  async verifyDetailedAnalysis(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log("\n🔍 Testing Detailed Analysis API...");

      const result = await chapterAnalyticsService.getDetailedAnalysis({
        quiz_id: TEST_DATA.quiz_id,
        user_id: TEST_DATA.user_id,
      });

      // Verify data structure
      if (
        !result.quiz_info?.quiz_id ||
        !result.student_info?.user_id ||
        !result.chapter_analysis
      ) {
        throw new Error("Invalid data structure returned");
      }

      // Verify chapters have required fields
      const allChapters = [
        ...(result.chapter_analysis.strengths || []),
        ...(result.chapter_analysis.weaknesses || []),
      ];
      if (allChapters.length > 0) {
        const firstChapter = allChapters[0];
        if (
          !firstChapter.chapter_id ||
          !firstChapter.chapter_name ||
          typeof firstChapter.accuracy_percentage !== "number"
        ) {
          throw new Error("Chapter data structure is invalid");
        }
      }

      const duration = Date.now() - startTime;
      this.addResult(
        "Detailed Analysis API",
        "PASS",
        `Successfully retrieved analysis for quiz ${TEST_DATA.quiz_id}`,
        duration
      );

      // Test loading state management
      const loadingState = chapterAnalyticsService.getLoadingState(
        "detailed_analysis",
        {
          quiz_id: TEST_DATA.quiz_id,
          user_id: TEST_DATA.user_id,
        }
      );

      if (
        loadingState.lastFetch &&
        !loadingState.isLoading &&
        !loadingState.error
      ) {
        this.addResult(
          "Loading State Management",
          "PASS",
          "Loading states are properly managed"
        );
      } else {
        this.addResult(
          "Loading State Management",
          "FAIL",
          "Loading states not properly updated"
        );
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      this.addResult(
        "Detailed Analysis API",
        "FAIL",
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
        duration
      );
    }
  }

  async verifyComprehensiveAnalysis(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log("\n📊 Testing Comprehensive Analysis API...");

      const result = await chapterAnalyticsService.getComprehensiveAnalysis({
        subject_id: TEST_DATA.subject_id,
        user_id: TEST_DATA.user_id,
      });

      // Verify data structure
      if (
        !result.subject_info ||
        !result.student_info ||
        !result.chapter_analysis
      ) {
        throw new Error("Invalid comprehensive analysis data structure");
      }

      const duration = Date.now() - startTime;
      this.addResult(
        "Comprehensive Analysis API",
        "PASS",
        `Successfully retrieved comprehensive analysis for subject ${TEST_DATA.subject_id}`,
        duration
      );
    } catch (error) {
      const duration = Date.now() - startTime;
      this.addResult(
        "Comprehensive Analysis API",
        "FAIL",
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
        duration
      );
    }
  }

  async verifyTeacherAnalytics(): Promise<void> {
    const startTime = Date.now();
    try {
      console.log("\n👨‍🏫 Testing Teacher Analytics API...");

      const result = await chapterAnalyticsService.getTeacherAnalytics({
        quiz_id: TEST_DATA.quiz_id,
        include_student_details: true,
        include_recommendations: true,
      });

      // Verify data structure
      if (
        !result.quiz_id ||
        !result.class_info ||
        !result.chapter_performance
      ) {
        throw new Error("Invalid teacher analytics data structure");
      }

      const duration = Date.now() - startTime;
      this.addResult(
        "Teacher Analytics API",
        "PASS",
        `Successfully retrieved teacher analytics for quiz ${TEST_DATA.quiz_id}`,
        duration
      );
    } catch (error) {
      const duration = Date.now() - startTime;
      this.addResult(
        "Teacher Analytics API",
        "FAIL",
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
        duration
      );
    }
  }

  async verifyBackwardCompatibility(): Promise<void> {
    console.log("\n🔄 Testing Backward Compatibility...");

    try {
      // Test that old radar APIs still work but show deprecation warnings
      const originalWarn = console.warn;
      const warnings: string[] = [];

      // Mock console.warn to capture warnings
      console.warn = (message: string) => {
        warnings.push(message);
        originalWarn(message); // Still show the warning
      };

      await quizService.getCurrentUserRadarData(TEST_DATA.quiz_id);

      if (
        warnings.some(
          (warning) =>
            warning.includes("DEPRECATED") &&
            warning.includes("getCurrentUserRadarData")
        )
      ) {
        this.addResult(
          "Deprecation Warnings",
          "PASS",
          "Deprecation warnings are properly displayed"
        );
      } else {
        this.addResult(
          "Deprecation Warnings",
          "FAIL",
          "Deprecation warnings not shown"
        );
      }

      // Restore original console.warn
      console.warn = originalWarn;

      this.addResult(
        "Backward Compatibility",
        "PASS",
        "Old radar APIs still functional"
      );
    } catch (error) {
      this.addResult(
        "Backward Compatibility",
        "FAIL",
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  async verifyErrorHandling(): Promise<void> {
    console.log("\n🚨 Testing Error Handling...");

    try {
      // Test with invalid data to trigger errors
      await chapterAnalyticsService.getDetailedAnalysis({
        quiz_id: -1,
        user_id: -1,
      });

      this.addResult(
        "Error Handling",
        "FAIL",
        "Should have thrown error for invalid data"
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes("analytics")) {
        this.addResult(
          "Error Handling",
          "PASS",
          "Proper error handling for invalid requests"
        );
      } else {
        this.addResult("Error Handling", "FAIL", "Unexpected error format");
      }
    }
  }

  async verifyRetryLogic(): Promise<void> {
    console.log("\n🔄 Testing Retry Logic...");

    try {
      const startTime = Date.now();

      // Test retry with valid data (should succeed on first try)
      await chapterAnalyticsService.getDetailedAnalysisWithRetry(
        {
          quiz_id: TEST_DATA.quiz_id,
          user_id: TEST_DATA.user_id,
        },
        2
      );

      const duration = Date.now() - startTime;
      this.addResult(
        "Retry Logic",
        "PASS",
        "Retry wrapper works correctly",
        duration
      );
    } catch (error) {
      this.addResult(
        "Retry Logic",
        "FAIL",
        `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  async runAllTests(): Promise<void> {
    console.log("🚀 Starting Chapter Analytics Integration Verification...\n");

    await this.verifyDetailedAnalysis();
    await this.verifyComprehensiveAnalysis();
    await this.verifyTeacherAnalytics();
    await this.verifyBackwardCompatibility();
    await this.verifyErrorHandling();
    await this.verifyRetryLogic();

    this.printSummary();
  }

  private printSummary(): void {
    console.log("\n📋 VERIFICATION SUMMARY");
    console.log("========================");

    const passed = this.results.filter((r) => r.status === "PASS").length;
    const failed = this.results.filter((r) => r.status === "FAIL").length;
    const skipped = this.results.filter((r) => r.status === "SKIP").length;

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏭️ Skipped: ${skipped}`);
    console.log(`📊 Total: ${this.results.length}`);

    if (failed > 0) {
      console.log("\n❌ FAILED TESTS:");
      this.results
        .filter((r) => r.status === "FAIL")
        .forEach((r) => console.log(`  - ${r.test}: ${r.message}`));
    }

    console.log(
      `\n${failed === 0 ? "🎉 ALL TESTS PASSED!" : "⚠️ SOME TESTS FAILED"}`
    );
  }
}

// Export for use in other scripts
export { ChapterAnalyticsVerifier };

// Run verification if this script is executed directly
if (require.main === module) {
  const verifier = new ChapterAnalyticsVerifier();
  verifier.runAllTests().catch(console.error);
}
