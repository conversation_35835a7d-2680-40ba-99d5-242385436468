'use strict';

const express = require('express');
const router = express.Router();
const AvatarCustomizationController = require('../controllers/avatarCustomizationController');
const { authenticateToken, authorize } = require('../middleware/authMiddleware');

// =====================================================
// AVATAR CUSTOMIZATION ROUTES
// =====================================================



router.post('/initialize', 
    authenticateToken, 
    AvatarCustomizationController.initializeAvatarSystem
);



router.get('/my-data', 
    authenticateToken, 
    AvatarCustomizationController.getUserAvatarData
);


router.get('/available-items', 
    authenticateToken, 
    AvatarCustomizationController.getAvailableItems
);



router.get('/inventory/:itemType', 
    authenticateToken, 
    AvatarCustomizationController.getUserInventoryByType
);



router.post('/equip', 
    authenticateToken, 
    AvatarCustomizationController.equipItem
);



router.post('/unequip', 
    authenticateToken, 
    AvatarCustomizationController.unequipItem
);



router.get('/customization', 
    authenticateToken, 
    AvatarCustomizationController.getUserCustomization
);



router.put('/customization', 
    authenticateToken, 
    AvatarCustomizationController.updateCustomizationSettings
);



router.get('/display-info/:userId?', 
    authenticateToken, 
    AvatarCustomizationController.getUserDisplayInfo
);



router.get('/collection-progress', 
    authenticateToken, 
    AvatarCustomizationController.getCollectionProgress
);

// =====================================================
// BROWSING ROUTES (Public/Semi-Public)
// =====================================================



router.get('/avatars', 
    authenticateToken, 
    AvatarCustomizationController.getAllAvatars
);



router.get('/frames', 
    authenticateToken, 
    AvatarCustomizationController.getAllFrames
);



router.get('/emojis', 
    authenticateToken, 
    AvatarCustomizationController.getAllEmojis
);


module.exports = router;
