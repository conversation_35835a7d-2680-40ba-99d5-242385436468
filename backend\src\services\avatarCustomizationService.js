'use strict';

const { 
    <PERSON><PERSON>, 
    <PERSON><PERSON><PERSON>rame, 
    NameEffect, 
    Emoji, 
    UserInventory, 
    UserCustomization,
    User,
    UserLevel
} = require('../models');
const { Op } = require('sequelize');

class AvatarCustomizationService {
    /**
     * Initialize avatar system for new user
     * @param {number} userId - User ID
     * @returns {Object} Initialization result
     */
    static async initializeUserAvatarSystem(userId) {
        try {
            // Get default items
            const defaultAvatars = await Avatar.getDefaultAvatars();
            const defaultFrame = await AvatarFrame.getDefaultFrame();
            const defaultEmojis = await Emoji.getDefaultEmojis();

            // Add default items to inventory
            const inventoryPromises = [];

            // Add default avatars
            for (const avatar of defaultAvatars) {
                inventoryPromises.push(
                    UserInventory.addItemToInventory(
                        userId, 'AVATAR', avatar.avatar_id, 'DEFAULT'
                    )
                );
            }

            // Add default frame
            if (defaultFrame) {
                inventoryPromises.push(
                    UserInventory.addItemToInventory(
                        userId, 'FRAME', defaultFrame.frame_id, 'DEFAULT'
                    )
                );
            }

            // Add default emojis
            for (const emoji of defaultEmojis) {
                inventoryPromises.push(
                    UserInventory.addItemToInventory(
                        userId, 'EMOJI', emoji.emoji_id, 'DEFAULT'
                    )
                );
            }

            await Promise.all(inventoryPromises);

            // Initialize customization with first default avatar and frame
            const firstAvatar = defaultAvatars[0];
            await UserCustomization.initializeUserCustomization(userId);

            return {
                success: true,
                message: 'Avatar system initialized successfully',
                data: {
                    default_avatars_count: defaultAvatars.length,
                    default_frame: defaultFrame ? defaultFrame.frame_name : null,
                    default_emojis_count: defaultEmojis.length,
                    equipped_avatar: firstAvatar ? firstAvatar.avatar_name : null
                }
            };
        } catch (error) {
            console.error('Error initializing avatar system:', error);
            return {
                success: false,
                message: 'Failed to initialize avatar system',
                error: error.message
            };
        }
    }

    /**
     * Get user's complete avatar collection and customization
     * @param {number} userId - User ID
     * @returns {Object} User's avatar data
     */
    static async getUserAvatarData(userId) {
        try {
            // Get user's customization
            const customization = await UserCustomization.getUserCustomization(userId);
            
            // Get user's complete inventory
            const inventory = await UserInventory.getUserCompleteInventory(userId);
            
            // Get inventory statistics
            const stats = await UserInventory.getInventoryStatistics(userId);

            // Get user level for unlock calculations
            const userLevel = await UserLevel.findOne({
                where: { user_id: userId },
                attributes: ['current_level', 'tier_name']
            });

            return {
                success: true,
                data: {
                    customization: customization ? customization.getFormattedInfo() : null,
                    inventory: inventory,
                    statistics: stats,
                    user_level: userLevel ? userLevel.current_level : 1,
                    user_tier: userLevel ? userLevel.tier_name : 'Wood'
                }
            };
        } catch (error) {
            console.error('Error getting user avatar data:', error);
            return {
                success: false,
                message: 'Failed to get user avatar data',
                error: error.message
            };
        }
    }

    /**
     * Get available items for user to unlock or purchase
     * @param {number} userId - User ID
     * @returns {Object} Available items
     */
    static async getAvailableItems(userId) {
        try {
            // Get user level
            const userLevel = await UserLevel.findOne({
                where: { user_id: userId },
                attributes: ['current_level', 'tier_name']
            });

            const currentLevel = userLevel ? userLevel.current_level : 1;
            const currentTier = userLevel ? userLevel.tier_name : 'Wood';

            // Get user's current inventory
            const userInventory = await UserInventory.findAll({
                where: { user_id: userId },
                attributes: ['item_type', 'item_id']
            });

            const ownedItems = new Set(
                userInventory.map(item => `${item.item_type}_${item.item_id}`)
            );

            // Get all available items
            const [avatars, frames, nameEffects, emojis] = await Promise.all([
                Avatar.getAvailableAvatars(),
                AvatarFrame.getAvailableFrames(),
                NameEffect.getAvailableNameEffects(),
                Emoji.getAvailableEmojis()
            ]);

            // Filter and categorize items
            const categorizeItems = (items, itemType) => {
                const owned = [];
                const unlockable = [];
                const locked = [];

                items.forEach(item => {
                    const itemKey = `${itemType}_${item[`${itemType.toLowerCase()}_id`] || item.avatar_id || item.frame_id || item.effect_id || item.emoji_id}`;
                    
                    if (ownedItems.has(itemKey)) {
                        owned.push(item.getFormattedInfo());
                    } else {
                        let canUnlock = false;
                        
                        if (itemType === 'AVATAR') {
                            canUnlock = item.canBeUnlockedBy(currentLevel);
                        } else if (itemType === 'FRAME') {
                            canUnlock = item.canBeUnlockedBy(currentLevel, currentTier);
                        } else if (itemType === 'NAME_EFFECT') {
                            canUnlock = item.canBeUnlockedBy(currentLevel);
                        } else if (itemType === 'EMOJI') {
                            canUnlock = item.canBeUnlockedBy(currentLevel);
                        }

                        if (canUnlock) {
                            unlockable.push(item.getFormattedInfo());
                        } else {
                            locked.push(item.getFormattedInfo());
                        }
                    }
                });

                return { owned, unlockable, locked };
            };

            return {
                success: true,
                data: {
                    user_level: currentLevel,
                    user_tier: currentTier,
                    avatars: categorizeItems(avatars, 'AVATAR'),
                    frames: categorizeItems(frames, 'FRAME'),
                    name_effects: categorizeItems(nameEffects, 'NAME_EFFECT'),
                    emojis: categorizeItems(emojis, 'EMOJI')
                }
            };
        } catch (error) {
            console.error('Error getting available items:', error);
            return {
                success: false,
                message: 'Failed to get available items',
                error: error.message
            };
        }
    }

    /**
     * Unlock items for user based on level progression
     * @param {number} userId - User ID
     * @param {number} newLevel - New level reached
     * @param {string} newTier - New tier reached
     * @returns {Object} Unlocked items
     */
    static async unlockItemsByLevel(userId, newLevel, newTier) {
        try {
            const unlockedItems = [];

            // Unlock avatars by level
            const unlockableAvatars = await Avatar.getUnlockableAvatarsByLevel(newLevel);
            for (const avatar of unlockableAvatars) {
                const existing = await UserInventory.checkUserOwnsItem(userId, 'AVATAR', avatar.avatar_id);
                if (!existing) {
                    await UserInventory.addItemToInventory(
                        userId, 'AVATAR', avatar.avatar_id, 'LEVEL_UP'
                    );
                    unlockedItems.push({
                        type: 'AVATAR',
                        item: avatar.getFormattedInfo()
                    });
                }
            }

            // Unlock frames by tier
            const tierFrames = await AvatarFrame.getFramesByTier(newTier);
            for (const frame of tierFrames) {
                const existing = await UserInventory.checkUserOwnsItem(userId, 'FRAME', frame.frame_id);
                if (!existing) {
                    await UserInventory.addItemToInventory(
                        userId, 'FRAME', frame.frame_id, 'TIER_UNLOCK'
                    );
                    unlockedItems.push({
                        type: 'FRAME',
                        item: frame.getFormattedInfo()
                    });
                }
            }

            // Unlock name effects by level
            const nameEffect = await NameEffect.getNameEffectForLevel(newLevel);
            if (nameEffect) {
                const existing = await UserInventory.checkUserOwnsItem(userId, 'NAME_EFFECT', nameEffect.effect_id);
                if (!existing) {
                    await UserInventory.addItemToInventory(
                        userId, 'NAME_EFFECT', nameEffect.effect_id, 'LEVEL_UP'
                    );
                    unlockedItems.push({
                        type: 'NAME_EFFECT',
                        item: nameEffect.getFormattedInfo()
                    });
                }
            }

            // Unlock emojis by level
            const unlockableEmojis = await Emoji.getUnlockableEmojisByLevel(newLevel);
            for (const emoji of unlockableEmojis) {
                const existing = await UserInventory.checkUserOwnsItem(userId, 'EMOJI', emoji.emoji_id);
                if (!existing) {
                    await UserInventory.addItemToInventory(
                        userId, 'EMOJI', emoji.emoji_id, 'LEVEL_UP'
                    );
                    unlockedItems.push({
                        type: 'EMOJI',
                        item: emoji.getFormattedInfo()
                    });
                }
            }

            return {
                success: true,
                message: `Unlocked ${unlockedItems.length} new items`,
                data: {
                    unlocked_items: unlockedItems,
                    level: newLevel,
                    tier: newTier
                }
            };
        } catch (error) {
            console.error('Error unlocking items by level:', error);
            return {
                success: false,
                message: 'Failed to unlock items',
                error: error.message
            };
        }
    }

    /**
     * Equip item for user
     * @param {number} userId - User ID
     * @param {string} itemType - Item type (avatar, frame, name_effect)
     * @param {number} itemId - Item ID
     * @returns {Object} Equip result
     */
    static async equipItem(userId, itemType, itemId) {
        try {
            let success = false;
            let message = '';

            switch (itemType.toLowerCase()) {
                case 'avatar':
                    success = await UserCustomization.equipAvatar(userId, itemId);
                    message = success ? 'Avatar equipped successfully' : 'Failed to equip avatar';
                    break;
                
                case 'frame':
                    success = await UserCustomization.equipFrame(userId, itemId);
                    message = success ? 'Frame equipped successfully' : 'Failed to equip frame';
                    break;
                
                case 'name_effect':
                    success = await UserCustomization.equipNameEffect(userId, itemId);
                    message = success ? 'Name effect equipped successfully' : 'Failed to equip name effect';
                    break;
                
                default:
                    return {
                        success: false,
                        message: 'Invalid item type'
                    };
            }

            if (success) {
                // Get updated customization
                const customization = await UserCustomization.getUserCustomization(userId);
                return {
                    success: true,
                    message: message,
                    data: customization ? customization.getFormattedInfo() : null
                };
            } else {
                return {
                    success: false,
                    message: message
                };
            }
        } catch (error) {
            console.error('Error equipping item:', error);
            return {
                success: false,
                message: 'Failed to equip item',
                error: error.message
            };
        }
    }

    /**
     * Unequip item for user
     * @param {number} userId - User ID
     * @param {string} itemType - Item type (avatar, frame, name_effect)
     * @returns {Object} Unequip result
     */
    static async unequipItem(userId, itemType) {
        try {
            const success = await UserCustomization.unequipItem(userId, itemType);
            
            if (success) {
                const customization = await UserCustomization.getUserCustomization(userId);
                return {
                    success: true,
                    message: `${itemType} unequipped successfully`,
                    data: customization ? customization.getFormattedInfo() : null
                };
            } else {
                return {
                    success: false,
                    message: `Failed to unequip ${itemType}`
                };
            }
        } catch (error) {
            console.error('Error unequipping item:', error);
            return {
                success: false,
                message: 'Failed to unequip item',
                error: error.message
            };
        }
    }

    /**
     * Get user display info for leaderboards
     * @param {number} userId - User ID
     * @returns {Object} Display info
     */
    static async getUserDisplayInfo(userId) {
        try {
            const displayInfo = await UserCustomization.getUserDisplayInfo(userId);
            
            if (!displayInfo) {
                return {
                    success: false,
                    message: 'User display info not found'
                };
            }

            return {
                success: true,
                data: displayInfo
            };
        } catch (error) {
            console.error('Error getting user display info:', error);
            return {
                success: false,
                message: 'Failed to get user display info',
                error: error.message
            };
        }
    }

    /**
     * Get collection progress for user
     * @param {number} userId - User ID
     * @returns {Object} Collection progress
     */
    static async getCollectionProgress(userId) {
        try {
            // Get total available items
            const [totalAvatars, totalFrames, totalEmojis, totalNameEffects] = await Promise.all([
                Avatar.count({ where: { is_active: true } }),
                AvatarFrame.count({ where: { is_active: true } }),
                Emoji.count({ where: { is_active: true } }),
                NameEffect.count({ where: { is_active: true } })
            ]);

            // Get user's inventory stats
            const stats = await UserInventory.getInventoryStatistics(userId);

            const progress = {
                avatars: {
                    owned: stats.by_type.avatars.count,
                    total: totalAvatars,
                    percentage: totalAvatars > 0 ? Math.round((stats.by_type.avatars.count / totalAvatars) * 100) : 0
                },
                frames: {
                    owned: stats.by_type.frames.count,
                    total: totalFrames,
                    percentage: totalFrames > 0 ? Math.round((stats.by_type.frames.count / totalFrames) * 100) : 0
                },
                emojis: {
                    owned: stats.by_type.emojis.count,
                    total: totalEmojis,
                    percentage: totalEmojis > 0 ? Math.round((stats.by_type.emojis.count / totalEmojis) * 100) : 0
                },
                name_effects: {
                    owned: stats.by_type.name_effects.count,
                    total: totalNameEffects,
                    percentage: totalNameEffects > 0 ? Math.round((stats.by_type.name_effects.count / totalNameEffects) * 100) : 0
                },
                overall: {
                    owned: stats.total_items,
                    total: totalAvatars + totalFrames + totalEmojis + totalNameEffects,
                    percentage: 0
                }
            };

            progress.overall.percentage = progress.overall.total > 0 ? 
                Math.round((progress.overall.owned / progress.overall.total) * 100) : 0;

            return {
                success: true,
                data: progress
            };
        } catch (error) {
            console.error('Error getting collection progress:', error);
            return {
                success: false,
                message: 'Failed to get collection progress',
                error: error.message
            };
        }
    }
}

module.exports = AvatarCustomizationService;
