const express = require('express');
const router = express.Router();
const { authenticateToken, authorize } = require('../middleware/authMiddleware');

// Test with simple handlers first
router.get('/balance', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Balance endpoint' });
});

router.get('/transactions', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Transactions endpoint' });
});

router.get('/list', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'List endpoint' });
});

router.get('/stats', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Stats endpoint' });
});

router.get('/earning-sources', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Earning sources endpoint' });
});

router.get('/leaderboard', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Leaderboard endpoint' });
});

router.post('/initialize', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Initialize endpoint' });
});

router.post('/sync', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Sync endpoint' });
});

router.post('/daily-login', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Daily login endpoint' });
});

router.post('/spend', authenticateToken, (req, res) => {
    res.json({ success: true, message: 'Spend endpoint' });
});

router.post('/award', authenticateToken, authorize(['admin']), (req, res) => {
    res.json({ success: true, message: 'Award endpoint' });
});

module.exports = router;
